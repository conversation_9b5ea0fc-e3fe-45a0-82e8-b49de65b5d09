package com.kbao.kbcelms.controller.opportunity;

import com.kbao.commons.web.Result;
import com.kbao.kbcelms.opportunityorder.entity.OpportunityOrder;
import com.kbao.kbcelms.opportunityorder.service.OpportunityOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 机会保单Controller
 * @Author: luobb
 */
@Slf4j
@Api(tags = {"机会保单管理"})
@RestController
@RequestMapping("/api/opportunity/order")
public class OpportunityOrderController {

    @Autowired
    private OpportunityOrderService opportunityOrderService;

    /**
     * 新增机会保单
     * @param opportunityOrder
     * @return
     */
    @ApiOperation("新增机会保单")
    @PostMapping("/add")
    public Result<OpportunityOrder> addOpportunityOrder(@RequestBody OpportunityOrder opportunityOrder) {
        OpportunityOrder savedOrder = this.opportunityOrderService.addOpportunityOrder(opportunityOrder);
        return Result.succeed(savedOrder, "新增成功");
    }

    /**
     * 修改机会保单
     * @param opportunityOrder
     * @return
     */
    @ApiOperation("修改机会保单")
    @PostMapping("/update")
    public Result<OpportunityOrder> updateOpportunityOrder(@RequestBody OpportunityOrder opportunityOrder) {
        OpportunityOrder updatedOrder = this.opportunityOrderService.updateOpportunityOrder(opportunityOrder);
        return Result.succeed(updatedOrder, "修改成功");
    }

    /**
     * 删除机会保单
     * @param opportunityOrder
     * @return
     */
    @ApiOperation("删除机会保单")
    @PostMapping("/delete")
    public Result<OpportunityOrder> deleteOpportunityOrder(@RequestBody OpportunityOrder opportunityOrder) {
        this.opportunityOrderService.deleteOpportunityOrder(opportunityOrder.getId());
        return Result.succeed(null, "删除成功");
    }

    /**
     * 查询机会保单
     * @param opportunityOrder
     * @return
     */
    @ApiOperation("查询机会保单")
    @PostMapping("/list")
    public Result<List<OpportunityOrder>> queryOpportunityOrder(@RequestBody OpportunityOrder opportunityOrder) {
        List<OpportunityOrder> orderList = this.opportunityOrderService.queryOpportunityOrderListByOpportunityId(opportunityOrder.getOpportunityId());
        return Result.succeed(orderList, "查询成功");
    }
}