package com.kbao.kbcelms.opportunitydetail.dao;

import java.util.*;
import java.util.Map;
import com.kbao.kbcbsc.dao.sql.BaseMapper;
import com.kbao.kbcelms.opportunitydetail.entity.OpportunityDetail;
import org.apache.ibatis.annotations.Param;

public interface OpportunityDetailMapper  extends BaseMapper<OpportunityDetail, Integer>{

    /**
     * 根据机会id查询机会明细
     *
     * @param opportunityId
     * @return
     */
    OpportunityDetail selectByOpportunityId(@Param("opportunityId")Integer opportunityId,@Param("tenantId")String tenantId);

    /**
     * 新增：通过opportunity_id更新log_time（允许为空）
     *
     * @param params
     */
    void updateLogTimeByOpportunityId(Map<String,Object> params);
}