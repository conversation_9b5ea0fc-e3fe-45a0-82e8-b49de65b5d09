package com.kbao.kbcelms.opportunitydetail.service;


import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


import com.kbao.commons.exception.BusinessException;
import com.kbao.kbcelms.opportunitydetail.model.OpportunityDetailVO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import com.kbao.kbcelms.opportunitydetail.entity.OpportunityDetail;
import com.kbao.kbcelms.opportunitydetail.dao.OpportunityDetailMapper;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;


@Service
public class OpportunityDetailService extends BaseSQLServiceImpl<OpportunityDetail, Integer,OpportunityDetailMapper> {

    /**
     * 根据机会id查询机会项目明细
     *
     * @param opportunityId
     * @param tenantId
     * @return
     */
    public OpportunityDetail selectByOpportunityId(Integer opportunityId,String tenantId){
        if(opportunityId == null){
            throw new BusinessException("机会id不能为空");
        }

        return this.mapper.selectByOpportunityId(opportunityId,tenantId);
    }

    /**
     * 新增或修改机会配置
     *
     * @param detailVO
     * @param tenantId
     * @param userId
     */
    public void saveByOpportunityId(OpportunityDetailVO detailVO, String tenantId,String userId){
        if(detailVO == null || detailVO.getOpportunityId() == null){
            throw new BusinessException("机会id不能为空");
        }

        OpportunityDetail detail = new OpportunityDetail();
        BeanUtils.copyProperties(detailVO,detail);
        detail.setTenantId(tenantId);
        detail.setIsDeleted(0);

        // 判断配置是否存在
        OpportunityDetail exists = this.mapper.selectByOpportunityId(detailVO.getOpportunityId(),tenantId);
        if(exists == null){
            detail.setCreateId(userId);
            detail.setCreateTime(new Date());
            this.mapper.insertSelective(detail);
        } else {
            detail.setUpdateId(userId);
            detail.setUpdateTime(new Date());
            this.mapper.updateByPrimaryKeySelective(detail);
        }

    }


    /**
     * 新增：通过opportunity_id更新log_time（允许为空）
     *
     * @param opportunityId
     * @param logTime
     */
    public void updateLogTimeByOpportunityId(String opportunityId, Date logTime) {
        Map<String,Object> params = new HashMap<>();
        params.put("opportunityId",opportunityId);
        params.put("logTime",logTime);
        this.mapper.updateLogTimeByOpportunityId(params);
    }
}
