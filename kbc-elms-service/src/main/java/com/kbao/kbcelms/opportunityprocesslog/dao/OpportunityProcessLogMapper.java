package com.kbao.kbcelms.opportunityprocesslog.dao;

import java.util.*;
import java.util.Map;
import com.kbao.kbcbsc.dao.sql.BaseMapper;
import com.kbao.kbcelms.opportunityprocesslog.entity.OpportunityProcessLog;
import org.apache.ibatis.annotations.Param;

public interface OpportunityProcessLogMapper  extends BaseMapper<OpportunityProcessLog, Integer>{
	
	/**
	 * 查询用户参与过的机会数量
	 * @param userId 用户ID
	 * @return 参与过的机会数量
	 */
	int countUserParticipatedOpportunities(@Param("userId") String userId);
}