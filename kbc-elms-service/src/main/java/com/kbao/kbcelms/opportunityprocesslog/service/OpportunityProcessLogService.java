package com.kbao.kbcelms.opportunityprocesslog.service;


import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.kbao.kbcelms.opportunityprocesslog.entity.OpportunityProcessLog;
import com.kbao.kbcelms.opportunityprocesslog.dao.OpportunityProcessLogMapper;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.tool.util.EmptyUtils;


@Service
public class OpportunityProcessLogService extends BaseSQLServiceImpl<OpportunityProcessLog, Integer,OpportunityProcessLogMapper> {

	/**
	 * 查询用户参与过的机会数量
	 * @param userId 用户ID
	 * @return 参与过的机会数量
	 */
	public int countUserParticipatedOpportunities(String userId) {
		if (EmptyUtils.isEmpty(userId)) {
			return 0;
		}
		return mapper.countUserParticipatedOpportunities(userId);
	}
}
