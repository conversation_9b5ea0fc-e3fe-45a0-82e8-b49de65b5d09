package com.kbao.kbcelms.bsc;

import com.aliyun.openservices.shade.com.alibaba.fastjson.JSON;
import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.exception.BusinessException;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.adapter.AppTenantClientAdapter;
import com.kbao.kbcbsc.adapter.DicItemsClientAdapter;
import com.kbao.kbcbsc.adapter.TenantClientAdapter;
import com.kbao.kbcbsc.adapter.UserClientAdapter;
import com.kbao.kbcbsc.appfilechannel.bean.AppFileChannelListVo;
import com.kbao.kbcbsc.apptenant.bean.AppTenantConfigResVo;
import com.kbao.kbcbsc.apptenant.bean.AppTenantIdReq;
import com.kbao.kbcbsc.apptenant.bean.AppTenantListVo;
import com.kbao.kbcbsc.dicitems.entity.DicItems;
import com.kbao.kbcbsc.tenant.bean.AppUserVo;
import com.kbao.kbcbsc.tenant.bean.TenantConfigInfoVo;
import com.kbao.kbcbsc.tenant.bean.TenantIdReq;
import com.kbao.kbcbsc.tenant.bean.TenantVo;
import com.kbao.kbcbsc.tenant.entity.Tenant;
import com.kbao.kbcbsc.user.model.UserIdReq;
import com.kbao.kbcbsc.util.BscUserUtils;
import com.kbao.tool.util.SysLoginUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/24 11:10
 */
@Service
@Slf4j
public class BscClientService {

    @Autowired
    private DicItemsClientAdapter dicItemsClientAdapter;

    @Autowired
    private UserClientAdapter userClientAdapter;

    @Autowired
    private TenantClientAdapter tenantClientAdapter;

    @Autowired
    AppTenantClientAdapter appTenantClientAdapter;

    /**
     * 获取快保云服字典列表
     *
     * @param dicCode:
     * <AUTHOR>
     * @date 2021/12/31 11:37
     */
    public Result<List<DicItems>> getDicItems(String dicCode) {
        Result<List<DicItems>> rs = dicItemsClientAdapter.getDicItems(dicCode);
        if(ResultStatusEnum.SUCCESS.getStatus().equals(rs.getResp_code())){
            return rs;
        }else{
            throw new BusinessException(rs.getResp_msg());
        }
    }

    /**
     * 获取当前登录用户在用户应用下的租户列表
     */
    public Result<List<AppTenantListVo>> getWebUserTenants() {
        AppUserVo req = new AppUserVo();
        req.setAppId(BscUserUtils.getUser().getFunction().getApplyId());
        req.setUserId(BscUserUtils.getUserId());
        return tenantClientAdapter.getAppUserTenantList(req);
    }

    public Result<List<UserIdReq>> getTenantUsers() {
        TenantIdReq tenantIdReq = new TenantIdReq();
        tenantIdReq.setTenantId(SysLoginUtils.getUser().getTenantId());
        return userClientAdapter.getTenantUsers(tenantIdReq);
    }

    /**
     * @param tenantId
     * @param appCode
     * @param fileType
     * @return
     * @Author: wangyl
     * @Description: 获取文件渠道配置
     * @Date: 2020/7/20 
     */
    public AppFileChannelListVo getFileChannelConfig(String tenantId, String appCode, String fileType) {
        AppTenantIdReq req = new AppTenantIdReq();
        req.setTenantId(tenantId);
        //if (!platformConfig.isWeb()) {
        req.setAppId(appCode);
        //}
        req.setAppCode(appCode);
        Result<AppTenantConfigResVo> rs = appTenantClientAdapter.getChannelConfigInfo(req);
        if (ResultStatusEnum.SUCCESS.getStatus().equals(rs.getResp_code())) {
            AppTenantConfigResVo appTenantConfigResVo = rs.getDatas();
            if (null != appTenantConfigResVo) {
                List<AppFileChannelListVo> fileChannelList = appTenantConfigResVo.getFileChannelList();
                if (CollectionUtils.isNotEmpty(fileChannelList)) {
                    for (AppFileChannelListVo vo : fileChannelList) {
                        if (StringUtils.equals(fileType, vo.getFileType())) {
                            return vo;
                        }
                    }
                }
            }
            log.error("getFileChannelConfig >> request >> 文件渠道未配置，请求：{}，结果：{}", JSON.toJSONString(req), JSON.toJSONString(rs));
            throw new BusinessException("文件渠道未配置");
        } else {
            throw new BusinessException(rs.getResp_msg());
        }
    }

    /**
     * @param tenantId
     * @return
     * @Author: wangyl
     * @Description: 获取租户信息
     * @Date: 2020/7/22 
     */
    public Tenant getTenant(String tenantId) {
        Result<TenantConfigInfoVo> rs = tenantClientAdapter.getTenantConfigInfo(new TenantVo(tenantId));
        if (ResultStatusEnum.SUCCESS.getStatus().equals(rs.getResp_code())) {
            TenantConfigInfoVo tenantConfigInfoVo = rs.getDatas();
            if (null != tenantConfigInfoVo.getTenant()) {
                return tenantConfigInfoVo.getTenant();
            }
            throw new BusinessException("消息渠道未配置");
        } else {
            throw new BusinessException(rs.getResp_msg());
        }
    }

}
