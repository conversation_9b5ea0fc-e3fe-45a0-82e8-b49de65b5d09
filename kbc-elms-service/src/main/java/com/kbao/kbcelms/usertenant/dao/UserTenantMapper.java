package com.kbao.kbcelms.usertenant.dao;

import com.kbao.kbcbsc.dao.sql.BaseMapper;
import com.kbao.kbcelms.usertenant.entity.UserTenant;
import org.apache.ibatis.annotations.Param;

public interface UserTenantMapper extends BaseMapper<UserTenant, Integer> {

    UserTenant getByUserIdAndTenantId(@Param("userId") String userId, @Param("tenantId") String tenantId);

    void setUserStatus(UserTenant userTenant);

}