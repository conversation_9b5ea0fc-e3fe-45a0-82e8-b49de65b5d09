package com.kbao.kbcelms.opportunity.service;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.exception.BusinessException;
import com.kbao.commons.web.PageRequest;
import com.kbao.commons.web.Result;
import com.kbao.kbcbpm.process.vo.*;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcelms.bpm.ElmsBpmWebService;
import com.kbao.kbcelms.enums.OpportunityStatusEnum;
import com.kbao.kbcelms.enums.OpportunityStepEnum;
import com.kbao.kbcelms.enums.RoleTypeEnum;
import com.kbao.kbcelms.opportunity.dao.OpportunityMapper;
import com.kbao.kbcelms.opportunity.entity.Opportunity;
import com.kbao.kbcelms.opportunity.vo.OpportunityDetailQuery;
import com.kbao.kbcelms.opportunity.vo.OpportunityDetailVO;
import com.kbao.kbcelms.opportunityprocess.service.OpportunityProcessService;
import com.kbao.kbcelms.opportunityprocesslog.entity.OpportunityProcessLog;
import com.kbao.kbcelms.opportunityprocesslog.service.OpportunityProcessLogService;
import com.kbao.kbcelms.opportunityteam.model.OpportunityTeamMember;
import com.kbao.kbcelms.opportunityteam.service.OpportunityTeamService;
import com.kbao.kbcelms.role.entity.Role;
import com.kbao.kbcelms.role.service.RoleService;
import com.kbao.kbcelms.user.entity.User;
import com.kbao.kbcelms.user.service.UserService;
import com.kbao.kbcelms.usertenant.entity.UserTenant;
import com.kbao.kbcelms.usertenant.service.UserTenantService;
import com.kbao.tool.util.EmptyUtils;
import com.kbao.tool.util.SysLoginUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
import com.kbao.kbcelms.opportunitydetail.service.OpportunityDetailService;
import com.kbao.kbcelms.opportunitydetail.entity.OpportunityDetail;


@Service
public class OpportunityService extends BaseSQLServiceImpl<Opportunity, Integer,OpportunityMapper> {

    @Autowired
    ElmsBpmWebService elmsBpmWebService;

    @Autowired
    RoleService roleService;

    @Autowired
    UserTenantService userTenantService;

    @Autowired
    OpportunityProcessLogService opportunityProcessLogService;

    @Autowired
    UserService userService;

    @Autowired
    OpportunityProcessService opportunityProcessService;

    @Autowired
    OpportunityTeamService opportunityTeamService;

    @Autowired
    OpportunityDetailService opportunityDetailService;

    /**
     * 查询我的待办任务
     *
     * @return
     */
    public Result<PageInfo<Opportunity>> getTodoTasksByUserAndCandidate (PageRequest reqVo){
        TaskQueryVO taskquery = new TaskQueryVO();
        taskquery.setTenantId(SysLoginUtils.getUser().getTenantId());
        taskquery.setCandidateUserId(SysLoginUtils.getUserId());
        taskquery.setUserId(SysLoginUtils.getUserId());
        taskquery.setPageNum(reqVo.getPageNum());
        taskquery.setPageSize(reqVo.getPageSize());
        List<Role> roles = roleService.getRolesByUserId(SysLoginUtils.getUser().getTenantId());
        //判断是否包含分公司统筹角色
        boolean isBranchCoordinator = roles.stream()
            .anyMatch(role -> role.getRoleType().equals(RoleTypeEnum.BRANCH_GM.getCode()));
        taskquery.setCandidateGroupIdIn(
            roles.stream()
                 .map(Role::getId)
                 .filter(Objects::nonNull)
                 .map(String::valueOf)
                 .collect(Collectors.toList())
        );
        //分公司统筹角色，则查询所有分公司统筹角色
        if (isBranchCoordinator) {
            taskquery.getCandidateGroupIdIn().add("org_type_all");
        }
        // 查询所有分公司统筹角色
        Map<String, Object> param = new HashMap<>();
        param.put("userId", SysLoginUtils.getUserId());
        param.put("tenantId", SysLoginUtils.getUser().getTenantId());
        List<UserTenant> userTenants = userTenantService.selectByParam(param);
        String organCodePath = userTenants.get(0).getOrganCodePath();
        String[] organCodes = organCodePath.split(",");
        for (String organCode : organCodes) {
            taskquery.getCandidateGroupIdIn().add("org_type_"+organCode);
        }
        Result<PageInfo<TaskVO>> tasks = elmsBpmWebService.getTodoTasksByUserAndCandidate(taskquery);
        if (ResultStatusEnum.isSuccess(tasks.getResp_code())){
            //提取processInstanceId合集
            List<String> processInstanceIds = tasks.getDatas().getList().stream()
                .map(TaskVO::getProcessInstanceId)
                .collect(Collectors.toList());
            //根据processInstanceId合集查询机会流程关联 查询 current_process_id 对应的 t_opportunity 对象合集
            List<Opportunity> opportunities = mapper.selectByProcessInstanceIds(processInstanceIds);
            PageInfo<Opportunity> pageInfo = new PageInfo<>();
            pageInfo.setPageNum(tasks.getDatas().getPageNum());
            pageInfo.setPageSize(tasks.getDatas().getPageSize());
            pageInfo.setPages(tasks.getDatas().getPages());
            pageInfo.setList(opportunities);
            pageInfo.setTotal(opportunities.size());
            return Result.succeedWith(pageInfo);
        }
        return null;
    }

    /**
     * 分公司领取任务
     * @param opportunityId 机会ID
     * @param processInstanceId 流程实例ID
     */
    public void acceptTask(Integer opportunityId, String processInstanceId) {
        // 查询机会信息并设置提交时间
        Opportunity opportunity = this.selectByPrimaryKey(opportunityId);
        if (opportunity != null) {
            // 查询机会明细并设置提交时间
            Map<String, Object> param = new HashMap<>();
            param.put("opportunityId", opportunityId);
            param.put("tenantId", opportunity.getTenantId());
            List<OpportunityDetail> details = opportunityDetailService.selectByParam(param);
            if (!details.isEmpty()) {
                OpportunityDetail detail = details.get(0);
                if (detail.getSubmitTime() == null) {
                    detail.setSubmitTime(new Date());
                    opportunityDetailService.updateByPrimaryKey(detail);
                }
            }
            
            opportunity.setUpdateId(SysLoginUtils.getUserId());
            opportunity.setUpdateTime(new Date());
            this.updateByPrimaryKey(opportunity);
        }
        //设置机会统筹人员
        opportunity.setCoordinator(SysLoginUtils.getUserId());
        this.updateByPrimaryKeySelective(opportunity);

        TaskUserUpdateVO vo = new TaskUserUpdateVO();
        vo.setProcessInstanceId(processInstanceId);
        vo.setAssignees(new ArrayList<>());
        vo.getAssignees().add(SysLoginUtils.getUserId());
        Result<List<TaskVO>> tasks = elmsBpmWebService.setTaskDealUserById(vo);
        TaskVO task = ResultStatusEnum.isSuccess(tasks.getResp_code())?tasks.getDatas().get(0):null;
        OpportunityProcessLog oplog = new OpportunityProcessLog();
        oplog.setOpportunityId(opportunityId);
        oplog.setProcessId(task.getProcessInstanceId());
        oplog.setOperaorId(SysLoginUtils.getUserId());
        oplog.setTargetId(SysLoginUtils.getUserId());
        oplog.setOperaorDesc("分公司统筹"+SysLoginUtils.getUser().getNickName()+"("+SysLoginUtils.getUser().getUserName()+")" + "领取了任务");
        opportunityProcessLogService.insert(oplog);
    }

    /**
     * 设置分公司统筹，任务转交其他统筹
     * @param opportunityId 机会ID
     * @param processInstanceId 流程实例ID
     * @param assignee 指派用户ID
     * @param assigneeRoleType 指派用户角色类型
     * @param assigneeOrg 指派用户机构
     */
    public void setBranchCoordination(Integer opportunityId, String processInstanceId, String assignee, Integer assigneeRoleType, String assigneeOrg) {
        TaskUserUpdateVO vo = new TaskUserUpdateVO();
        vo.setProcessInstanceId(processInstanceId);
        vo.setAssignees(new ArrayList<>());
        vo.getAssignees().add(assignee);
        Result<List<TaskVO>> tasks = elmsBpmWebService.setTaskDealUserById(vo);
        TaskVO task = ResultStatusEnum.isSuccess(tasks.getResp_code()) ? tasks.getDatas().get(0) : null;
        OpportunityProcessLog oplog = new OpportunityProcessLog();
        oplog.setOpportunityId(opportunityId);
        oplog.setProcessId(task.getProcessInstanceId());
        oplog.setOperaorId(SysLoginUtils.getUserId());
        oplog.setTargetId(assignee);
        //更新项目统筹人员
        Opportunity opportunity = new Opportunity();
        opportunity.setId(opportunityId);
        opportunity.setCoordinator(assignee);
        this.updateByPrimaryKeySelective(opportunity);
        //根据assignee查询对应userId的用户信息
        User userInfo = userService.getUserInfoByUserId(assignee);
        //北京分公司统筹 王年金（wangnj）指派机会给总公司统筹 张团财（zhangtc）
        oplog.setOperaorDesc("分公司统筹"+SysLoginUtils.getUser().getNickName()+"("+SysLoginUtils.getUser().getUserName()+")"+"将机会指派给"+assigneeOrg + RoleTypeEnum.getNameByCode(assigneeRoleType) + userInfo.getNickName() + "(" + userInfo.getBscUseName() + ")");
        opportunityProcessLogService.insert(oplog);
    }

    /**
     * 设置项目经理
     * @param opportunityId 机会ID
     * @param businessTenantId 业务租户ID
     * @param assignee 指派用户ID
     * @param assigneeRoleType 指派用户角色类型
     * @param assigneeOrg 指派用户机构
     */
    public void setOpportunityProjecter(Integer opportunityId,String businessTenantId, String assignee, Integer assigneeRoleType, String assigneeOrg) {
        Opportunity opportunity = this.selectByPrimaryKey(opportunityId);
        String processInstanceId = opportunityProcessService.selectByPrimaryKey(opportunity.getCurrentProcessId()).getBpmProcessId();
        //修改机会对应租户id
        if (!opportunity.getTenantId().equals(businessTenantId)) {
            opportunity.setTenantId(businessTenantId);
            updateOpportunityTenantId(opportunityId,businessTenantId,"指派项目经理变更租户归属");
            opportunity.setUpdateId(SysLoginUtils.getUserId());
            opportunity.setUpdateTime(new Date());
            this.updateByPrimaryKey(opportunity);
        }
        opportunity.setProjectManager(assignee);
        UserTenant pm = userTenantService.getByUserIdAndTenantId(assignee, businessTenantId);
        opportunity.setProjectManager(assignee);
        opportunity.setProjectOrgCode(pm.getOrganCode());
        opportunity.setProjectOrgName(pm.getOrganName());

        ProcessInstanceIdVO processInstanceIdVO = new ProcessInstanceIdVO();
        processInstanceIdVO.setProcessInstanceId(processInstanceId);
        Result<List<TaskVO>> result = elmsBpmWebService.getCurrentTaskInfoById(processInstanceIdVO);
        TaskVO task = result.getDatas().get(0);
        if (EmptyUtils.isNotEmpty(task.getAssignee())){
            if (!SysLoginUtils.getUserId().equals(task.getAssignee())){
                throw new BusinessException("当前用户非任务领取人，不得指派项目经理");
            }
        }
        //执行并完成统筹节点任务
        TaskCompleteVO tcvo = new TaskCompleteVO();
        tcvo.setTaskId(task.getId());
        tcvo.setProcessInstanceId(processInstanceId);
        tcvo.setAssignee(SysLoginUtils.getUserId());
        elmsBpmWebService.completeCurrentTasksByProcessInstanceId(tcvo);
        //指派后续任务为项目经理
        TaskUserUpdateVO vo = new TaskUserUpdateVO();
        vo.setProcessInstanceId(processInstanceId);
        vo.setAssignees(new ArrayList<>());
        vo.getAssignees().add(assignee);
        Result<List<TaskVO>> tasks = elmsBpmWebService.setTaskDealUserById(vo);
        task = ResultStatusEnum.isSuccess(tasks.getResp_code())?tasks.getDatas().get(0):null;
        OpportunityProcessLog oplog = new OpportunityProcessLog();
        oplog.setOpportunityId(opportunityId);
        oplog.setProcessId(task.getProcessInstanceId());
        oplog.setOperaorId(SysLoginUtils.getUserId());
        oplog.setTargetId(assignee);
        //根据assignee查询对应userId的用户信息
        User userInfo = userService.getUserInfoByUserId(assignee);
        //北京分公司统筹 王年金（wangnj）指派机会给总公司统筹 张团财（zhangtc）
        oplog.setOperaorDesc("分公司统筹"+SysLoginUtils.getUser().getNickName()+"("+SysLoginUtils.getUser().getUserName()+")"+"将机会指派给"+assigneeOrg + RoleTypeEnum.getNameByCode(assigneeRoleType) + userInfo.getNickName() + "(" + userInfo.getBscUseName() + ")");
        opportunityProcessLogService.insert(oplog);

        // 构造项目经理对象并初始化项目团队
        List<OpportunityTeamMember> members = new ArrayList<>();
        OpportunityTeamMember projectManager = new OpportunityTeamMember();
        projectManager.setUserId(assignee);
        projectManager.setOpportunityId(opportunityId);
        // 根据角色类型设置角色ID，这里假设项目经理的角色ID为2，实际需要根据业务逻辑调整
        projectManager.setRoleType(assigneeRoleType);
        projectManager.setIsDefault(1);
        members.add(projectManager);
        // 调用项目团队初始化方法
        opportunityTeamService.init(members, businessTenantId);
    }

    /**
     * 统一修改机会相关数据的租户ID
     * @param opportunityId 机会ID
     * @param newTenantId 新的租户ID
     * @param reason 修改原因
     */
    public void updateOpportunityTenantId(Integer opportunityId, String newTenantId, String reason) {
        if (EmptyUtils.isEmpty(opportunityId)) {
            throw new BusinessException("机会ID不能为空");
        }
        if (EmptyUtils.isEmpty(newTenantId)) {
            throw new BusinessException("新租户ID不能为空");
        }
        
        // 1. 修改机会表的租户ID
        Opportunity opportunity = this.selectByPrimaryKey(opportunityId);
        if (opportunity == null) {
            throw new BusinessException("机会不存在");
        }
        String oldTenantId = opportunity.getTenantId();
        opportunity.setTenantId(newTenantId);
        opportunity.setUpdateId(SysLoginUtils.getUserId());
        opportunity.setUpdateTime(new Date());
        this.updateByPrimaryKey(opportunity);
        
        // 2. 修改机会流程关联表的租户ID
        Map<String, Object> processParam = new HashMap<>();
        processParam.put("opportunityId", opportunityId);
        // 这里需要注入OpportunityProcessService来修改流程关联表
        opportunityProcessService.updateTenantIdByOpportunityId(opportunityId, newTenantId);
        
        // 3. 修改机会流程日志表的租户ID
        Map<String, Object> logParam = new HashMap<>();
        logParam.put("opportunityId", opportunityId);
        List<OpportunityProcessLog> logs = opportunityProcessLogService.selectByParam(logParam);
        for (OpportunityProcessLog log : logs) {
            log.setTenantId(newTenantId);
            log.setUpdateId(SysLoginUtils.getUserId());
            log.setUpdateTime(new Date());
            opportunityProcessLogService.updateByPrimaryKey(log);
        }
        
        // 4. 记录租户ID变更日志
        OpportunityProcessLog oplog = new OpportunityProcessLog();
        oplog.setOpportunityId(opportunityId);
        oplog.setTenantId(newTenantId);
        oplog.setOperaorId(SysLoginUtils.getUserId());
        oplog.setOperaorDesc("租户ID从 " + oldTenantId + " 变更为 " + newTenantId + "，原因：" + reason);
        oplog.setCreateTime(new Date());
        oplog.setIsDeleted(0);
        opportunityProcessLogService.insert(oplog);
    }
    
    /**
     * 暂停机会
     * @param opportunityId 机会ID
     * @param reason 暂停原因
     */
    public void suspendOpportunity(Integer opportunityId, String reason) {
        if (EmptyUtils.isEmpty(opportunityId)) {
            throw new BusinessException("机会ID不能为空");
        }
        if (EmptyUtils.isEmpty(reason)) {
            throw new BusinessException("暂停原因不能为空");
        }
        
        // 查询机会信息
        Opportunity opportunity = this.selectByPrimaryKey(opportunityId);
        if (opportunity == null) {
            throw new BusinessException("机会不存在");
        }
        
        // 检查机会状态是否允许暂停
        Integer currentStatus = opportunity.getStatus();
        if (OpportunityStatusEnum.SUSPENDED.getCode().equals(currentStatus)) {
            throw new BusinessException("机会已经处于暂停状态");
        }
        if (OpportunityStatusEnum.TERMINATED.getCode().equals(currentStatus)) {
            throw new BusinessException("已终止的机会不能暂停");
        }
        
        // 更新机会状态为暂停
        opportunity.setStatus(OpportunityStatusEnum.SUSPENDED.getCode());
        opportunity.setUpdateId(SysLoginUtils.getUserId());
        opportunity.setUpdateTime(new Date());
        this.updateByPrimaryKey(opportunity);
        
        // 记录暂停操作日志
        OpportunityProcessLog oplog = new OpportunityProcessLog();
        oplog.setOpportunityId(opportunityId);
        oplog.setTenantId(opportunity.getTenantId());
        oplog.setOperaorId(SysLoginUtils.getUserId());
        oplog.setOperaorDesc("机会暂停，原因：" + reason);
        oplog.setCreateTime(new Date());
        oplog.setIsDeleted(0);
        opportunityProcessLogService.insert(oplog);
    }
    
    /**
     * 恢复暂停的机会
     * @param opportunityId 机会ID
     * @param reason 恢复原因
     */
    public void resumeOpportunity(Integer opportunityId, String reason) {
        if (EmptyUtils.isEmpty(opportunityId)) {
            throw new BusinessException("机会ID不能为空");
        }
        if (EmptyUtils.isEmpty(reason)) {
            throw new BusinessException("恢复原因不能为空");
        }
        
        // 查询机会信息
        Opportunity opportunity = this.selectByPrimaryKey(opportunityId);
        if (opportunity == null) {
            throw new BusinessException("机会不存在");
        }
        
        // 检查机会状态是否为暂停
        Integer currentStatus = opportunity.getStatus();
        if (!OpportunityStatusEnum.SUSPENDED.getCode().equals(currentStatus)) {
            throw new BusinessException("只有暂停状态的机会才能恢复");
        }
        
        // 更新机会状态为已提交（恢复）
        opportunity.setStatus(OpportunityStatusEnum.SUBMITTED.getCode());
        opportunity.setUpdateId(SysLoginUtils.getUserId());
        opportunity.setUpdateTime(new Date());
        this.updateByPrimaryKey(opportunity);
        
        // 更新机会明细的提交时间
        Map<String, Object> param = new HashMap<>();
        param.put("opportunityId", opportunityId);
        param.put("tenantId", opportunity.getTenantId());
        List<OpportunityDetail> details = opportunityDetailService.selectByParam(param);
        if (!details.isEmpty()) {
            OpportunityDetail detail = details.get(0);
            detail.setSubmitTime(new Date());
            opportunityDetailService.updateByPrimaryKey(detail);
        }
        
        // 记录恢复操作日志
        OpportunityProcessLog oplog = new OpportunityProcessLog();
        oplog.setOpportunityId(opportunityId);
        oplog.setTenantId(opportunity.getTenantId());
        oplog.setOperaorId(SysLoginUtils.getUserId());
        oplog.setOperaorDesc("机会恢复，原因：" + reason);
        oplog.setCreateTime(new Date());
        oplog.setIsDeleted(0);
        opportunityProcessLogService.insert(oplog);
    }
    
    /**
     * 关闭机会
     * @param opportunityId 机会ID
     * @param reason 关闭原因
     */
    public void closeOpportunity(Integer opportunityId, String reason) {
        if (EmptyUtils.isEmpty(opportunityId)) {
            throw new BusinessException("机会ID不能为空");
        }
        if (EmptyUtils.isEmpty(reason)) {
            throw new BusinessException("关闭原因不能为空");
        }
        
        // 查询机会信息
        Opportunity opportunity = this.selectByPrimaryKey(opportunityId);
        if (opportunity == null) {
            throw new BusinessException("机会不存在");
        }
        
        // 检查机会状态是否允许关闭
        Integer currentStatus = opportunity.getStatus();
        if (OpportunityStatusEnum.TERMINATED.getCode().equals(currentStatus)) {
            throw new BusinessException("机会已经处于关闭状态");
        }
        
        // 更新机会状态为关闭
        opportunity.setStatus(OpportunityStatusEnum.TERMINATED.getCode());
        opportunity.setUpdateId(SysLoginUtils.getUserId());
        opportunity.setUpdateTime(new Date());
        this.updateByPrimaryKey(opportunity);
        
        // 记录关闭操作日志
        OpportunityProcessLog oplog = new OpportunityProcessLog();
        oplog.setOpportunityId(opportunityId);
        oplog.setTenantId(opportunity.getTenantId());
        oplog.setOperaorId(SysLoginUtils.getUserId());
        oplog.setOperaorDesc("机会关闭，原因：" + reason);
        oplog.setCreateTime(new Date());
        oplog.setIsDeleted(0);
        opportunityProcessLogService.insert(oplog);
    }

    /**
     * 重启关闭的机会
     * @param opportunityId 机会ID
     * @param reason 恢复原因
     * @param assignee 执行人
     * @param assigneeRoleType 角色类型
     */
    public void restartOpportunity(Integer opportunityId, String reason,String businessTenantId, String assignee, Integer assigneeRoleType, String assigneeOrg) {
        if (EmptyUtils.isEmpty(opportunityId)) {
            throw new BusinessException("机会ID不能为空");
        }
        if (EmptyUtils.isEmpty(reason)) {
            throw new BusinessException("重启原因不能为空");
        }

        // 查询机会信息
        Opportunity opportunity = this.selectByPrimaryKey(opportunityId);
        if (opportunity == null) {
            throw new BusinessException("机会不存在");
        }

        // 检查机会状态是否为暂停
        Integer currentStatus = opportunity.getStatus();
        if (!OpportunityStatusEnum.TERMINATED.getCode().equals(currentStatus)) {
            throw new BusinessException("只有关闭状态的机会才能重启");
        }

        //调用流程接口回滚机会步骤到 统筹跟进
        ProcessRollbackByPropertyVO rollback = new ProcessRollbackByPropertyVO();
        rollback.setPropertyName("stepType");
        rollback.setPropertyValue(OpportunityStepEnum.FOLLOW_UP.getValue());
        elmsBpmWebService.rollbackProcessByCustomProperty(rollback);

        // 记录恢复操作日志
        OpportunityProcessLog oplog = new OpportunityProcessLog();
        oplog.setOpportunityId(opportunityId);
        oplog.setTenantId(opportunity.getTenantId());
        oplog.setOperaorId(SysLoginUtils.getUserId());
        oplog.setOperaorDesc("机会重启，原因：" + reason);
        oplog.setCreateTime(new Date());
        oplog.setIsDeleted(0);
        opportunityProcessLogService.insert(oplog);

        //设置项目经理
        setOpportunityProjecter(opportunityId,businessTenantId,assignee,assigneeRoleType,assigneeOrg);

        // 更新机会状态为已提交（恢复）
        opportunity.setStatus(OpportunityStatusEnum.SUBMITTED.getCode());
        opportunity.setUpdateId(SysLoginUtils.getUserId());
        opportunity.setUpdateTime(new Date());
        opportunity.setProcessStep(OpportunityStepEnum.TEAM_UP.getValue());
        this.updateByPrimaryKey(opportunity);

        // 更新机会明细的提交时间
        Map<String, Object> param = new HashMap<>();
        param.put("opportunityId", opportunityId);
        param.put("tenantId", opportunity.getTenantId());
        List<OpportunityDetail> details = opportunityDetailService.selectByParam(param);
        if (!details.isEmpty()) {
            OpportunityDetail detail = details.get(0);
            detail.setSubmitTime(new Date());
            opportunityDetailService.updateByPrimaryKey(detail);
        }
    }

    /**
     * 查询机会详情列表
     * @param query 查询参数
     * @return 机会详情列表
     */
    public List<OpportunityDetailVO> queryOpportunityDetails(OpportunityDetailQuery query) {
        return this.mapper.selectOpportunityDetails(query);
    }

    /**
     * 分页查询机会详情列表
     * @param pageRequest 分页参数
     * @return 机会详情列表
     */
    public PageInfo<OpportunityDetailVO> queryOpportunityDetailsPage(PageRequest<OpportunityDetailQuery> pageRequest) {
        pageRequest.getParam().setTenantId(SysLoginUtils.getUser().getTenantId());
        PageHelper.startPage(pageRequest.getPageNum(), pageRequest.getPageSize());
        Page<OpportunityDetailVO> page = (Page<OpportunityDetailVO>) this.mapper.selectOpportunityDetails( pageRequest.getParam());
        return new PageInfo<>(page);
    }

    /**
     * 获取机会流程进度
     * @param opportunityId 机会ID
     * @return 流程进度列表
     */
    public List<ActivityInfoVO> getProcessProgress(Integer opportunityId){
        List<ActivityInfoVO> list = new ArrayList<>();
        Opportunity opportunity = this.selectByPrimaryKey(opportunityId);
        String processInstanceId = opportunityProcessService.selectByPrimaryKey(opportunity.getCurrentProcessId()).getBpmProcessId();
        Result<ProcessProgressVO> progress = elmsBpmWebService.getProcessProgress(processInstanceId);
        if (EmptyUtils.isEmpty(progress.getDatas())) {
            progress.getDatas().getCompletedActivities().forEach(obj->obj.setStatus("1"));
            list.addAll(progress.getDatas().getCompletedActivities());
            progress.getDatas().getCurrentActivities().forEach(obj->obj.setStatus("0"));
            list.addAll(progress.getDatas().getCurrentActivities());
            progress.getDatas().getPendingActivities().forEach(obj->obj.setStatus("0"));
            list.addAll(progress.getDatas().getPendingActivities());
        }
        return list;
    }
}
