package com.kbao.kbcelms.constant.dao;

import com.kbao.kbcbsc.dao.sql.BaseMapper;
import com.kbao.kbcelms.constant.entity.ConstantConfig;
import com.kbao.kbcelms.constant.vo.ConstantConfigVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 常数配置 Mapper 接口
 */
public interface ConstantConfigMapper extends BaseMapper<ConstantConfig, Long> {

    /**
     * 根据编码查询常数配置
     *
     * @param code 常数编码
     * @return 常数配置
     */
    ConstantConfig selectByCode(@Param("code") String code);

    /**
     * 检查编码是否存在
     *
     * @param code 常数编码
     * @param excludeId 排除的ID
     * @return 数量
     */
    int checkCodeExists(@Param("code") String code, @Param("excludeId") Long excludeId);

    /**
     * 更新常数配置状态
     *
     * @param id 常数配置ID
     * @param status 状态
     * @return 影响行数
     */
    int updateConstantConfigStatus(@Param("id") Long id, @Param("status") Integer status);

    /**
     * 分页查询常数配置列表（配合PageHelper使用）
     *
     * @param name 常数名称
     * @param code 常数编码
     * @param category 分类
     * @param dataType 数据类型
     * @param status 状态
     * @param isSystem 是否系统常数
     * @return 常数配置列表
     */
    List<ConstantConfigVO> selectConstantConfigListForPage(
            @Param("name") String name,
            @Param("code") String code,
            @Param("category") Integer category,
            @Param("dataType") String dataType,
            @Param("status") Integer status,
            @Param("isSystem") Integer isSystem
    );
}
