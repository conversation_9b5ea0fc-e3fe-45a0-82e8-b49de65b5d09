package com.kbao.kbcelms.constant.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.kbao.commons.web.PageRequest;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcelms.constant.dao.ConstantConfigMapper;
import com.kbao.kbcelms.constant.dto.ConstantConfigDTO;
import com.kbao.kbcelms.constant.dto.ConstantConfigQueryDTO;
import com.kbao.kbcelms.constant.entity.ConstantConfig;
import com.kbao.kbcelms.constant.enums.ConstantCategoryEnum;
import com.kbao.kbcelms.constant.vo.ConstantConfigVO;
import com.kbao.tool.util.SysLoginUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.List;

/**
 * 常数配置服务实现类
 */
@Slf4j
@Service
public class ConstantConfigService extends BaseSQLServiceImpl<ConstantConfig, Long, ConstantConfigMapper> {

    public PageInfo<ConstantConfigVO> getConstantConfigList(PageRequest<ConstantConfigQueryDTO> pageRequest) {
        log.info("分页查询常数配置列表 - pageRequest: {}", pageRequest);
        
        // 获取查询条件
        ConstantConfigQueryDTO param = pageRequest.getParam();
        String name = null;
        String code = null;
        Integer category = null;
        String dataType = null;
        Integer status = null;
        Integer isSystem = null;
        
        if (param != null) {
            name = param.getName();
            code = param.getCode();
            category = param.getCategory();
            dataType = param.getDataType();
            status = param.getStatus();
        }
        
        // 设置分页参数
        PageHelper.startPage(pageRequest.getPageNum(), pageRequest.getPageSize());
        
        // 查询数据（PageHelper会自动处理分页）
        List<ConstantConfigVO> list = mapper.selectConstantConfigListForPage(
                name, code, category, dataType, status, isSystem);
        
        // 处理描述字段
        if (!CollectionUtils.isEmpty(list)) {
            for (ConstantConfigVO vo : list) {
                processDescriptions(vo);
            }
        }
        
        return new PageInfo<>(list);
    }

    public ConstantConfigVO getConstantConfigDetail(Long id) {
        log.info("查询常数配置详情 - id: {}", id);
        
        if (id == null) {
            throw new RuntimeException("常数配置ID不能为空");
        }
        
        ConstantConfig constantConfig = this.selectByPrimaryKey(id);
        if (constantConfig == null) {
            throw new RuntimeException("常数配置不存在: " + id);
        }
        
        ConstantConfigVO vo = new ConstantConfigVO();
        BeanUtils.copyProperties(constantConfig, vo);
        processDescriptions(vo);
        
        return vo;
    }

    @Transactional(rollbackFor = Exception.class)
    public Long createConstantConfig(ConstantConfigDTO constantConfigDTO) {
        log.info("创建常数配置 - constantConfigDTO: {}", constantConfigDTO);
        
        // 验证编码唯一性
        if (checkCodeExists(constantConfigDTO.getCode(), null)) {
            throw new RuntimeException("常数编码已存在: " + constantConfigDTO.getCode());
        }
        
        // 验证分类有效性
        if (constantConfigDTO.getCategory() != null && 
            !ConstantCategoryEnum.isValidCode(constantConfigDTO.getCategory())) {
            throw new RuntimeException("无效的分类代码: " + constantConfigDTO.getCategory());
        }
        
        // 验证常数值格式
        validateConstantValue(constantConfigDTO.getDataType(), constantConfigDTO.getConstantValue());
        
        // 创建常数配置
        ConstantConfig constantConfig = new ConstantConfig();
        BeanUtils.copyProperties(constantConfigDTO, constantConfig);
        
        // 设置默认值
        if (constantConfig.getStatus() == null) {
            constantConfig.setStatus(1);
        }
        if (constantConfig.getUsageCount() == null) {
            constantConfig.setUsageCount(0);
        }
        
        // 设置创建信息
        constantConfig.setCreateUser(SysLoginUtils.getUserId());
        constantConfig.setUpdateUser(SysLoginUtils.getUserId());
        
        this.insert(constantConfig);
        
        log.info("常数配置创建成功 - id: {}", constantConfig.getId());
        return constantConfig.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean updateConstantConfig(Long id, ConstantConfigDTO constantConfigDTO) {
        log.info("更新常数配置 - id: {}, constantConfigDTO: {}", id, constantConfigDTO);
        
        // 检查常数配置是否存在
        ConstantConfig existingConfig = this.selectByPrimaryKey(id);
        if (existingConfig == null) {
            throw new RuntimeException("常数配置不存在: " + id);
        }
        
        // 验证编码唯一性（排除当前记录）
        if (checkCodeExists(constantConfigDTO.getCode(), id)) {
            throw new RuntimeException("常数编码已存在: " + constantConfigDTO.getCode());
        }
        
        // 验证分类有效性
        if (constantConfigDTO.getCategory() != null && 
            !ConstantCategoryEnum.isValidCode(constantConfigDTO.getCategory())) {
            throw new RuntimeException("无效的分类代码: " + constantConfigDTO.getCategory());
        }
        
        // 验证常数值格式
        validateConstantValue(constantConfigDTO.getDataType(), constantConfigDTO.getConstantValue());
        
        // 更新常数配置
        ConstantConfig constantConfig = new ConstantConfig();
        BeanUtils.copyProperties(constantConfigDTO, constantConfig);
        constantConfig.setId(id);
        constantConfig.setUpdateUser(SysLoginUtils.getUserId());
        
        int result = this.updateByPrimaryKeySelective(constantConfig);
        
        log.info("常数配置更新完成 - id: {}, result: {}", id, result);
        return result > 0;
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteConstantConfig(Long id) {
        log.info("删除常数配置 - id: {}", id);
        
        if (id == null) {
            throw new RuntimeException("常数配置ID不能为空");
        }
        
        // 检查常数配置是否存在
        ConstantConfig existingConfig = this.mapper.selectByPrimaryKey(id);
        if (existingConfig == null) {
            throw new RuntimeException("常数配置不存在: " + id);
        }
        int result = this.mapper.deleteByPrimaryKey(id);
        log.info("常数配置删除完成 - id: {}, result: {}", id, result);
        return result > 0;
    }























    /**
     * 检查编码是否存在
     *
     * @param code 常数编码
     * @param excludeId 排除的ID
     * @return 是否存在
     */
    public Boolean checkCodeExists(String code, Long excludeId) {
        if (!StringUtils.hasText(code)) {
            return false;
        }

        int count = mapper.checkCodeExists(code, excludeId);
        return count > 0;
    }

    /**
     * 更新常数配置状态
     *
     * @param id 常数配置ID
     * @param status 状态
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateConstantConfigStatus(Long id, Integer status) {
        log.info("更新常数配置状态 - id: {}, status: {}", id, status);

        if (id == null) {
            throw new RuntimeException("常数配置ID不能为空");
        }

        if (status == null || (status != 0 && status != 1)) {
            throw new RuntimeException("状态值无效，应为0或1");
        }

        // 检查常数配置是否存在
        ConstantConfig existingConfig = this.selectByPrimaryKey(id);
        if (existingConfig == null) {
            throw new RuntimeException("常数配置不存在: " + id);
        }

        int result = mapper.updateConstantConfigStatus(id, status);

        log.info("常数配置状态更新完成 - id: {}, status: {}, result: {}", id, status, result);
        return result > 0;
    }

    /**
     * 处理描述字段
     */
    private void processDescriptions(ConstantConfigVO vo) {
        if (vo == null) {
            return;
        }

        // 分类名称
        if (vo.getCategory() != null) {
            vo.setCategoryName(ConstantCategoryEnum.getNameByCode(vo.getCategory()));
        }

        // 数据类型描述
        if (StringUtils.hasText(vo.getDataType())) {
            switch (vo.getDataType()) {
                case "string":
                    vo.setDataTypeDesc("字符串");
                    break;
                case "number":
                    vo.setDataTypeDesc("数值");
                    break;
                case "boolean":
                    vo.setDataTypeDesc("布尔值");
                    break;
                case "date":
                    vo.setDataTypeDesc("日期");
                    break;
                case "json":
                    vo.setDataTypeDesc("JSON");
                    break;
                default:
                    vo.setDataTypeDesc(vo.getDataType());
                    break;
            }
        }

        // 状态描述
        if (vo.getStatus() != null) {
            vo.setStatusDesc(vo.getStatus() == 1 ? "启用" : "禁用");
        }


    }

    /**
     * 验证常数值格式
     */
    private void validateConstantValue(String dataType, String value) {
        if (!StringUtils.hasText(dataType) || !StringUtils.hasText(value)) {
            return;
        }

        try {
            switch (dataType) {
                case "number":
                    new BigDecimal(value);
                    break;
                case "boolean":
                    if (!"true".equalsIgnoreCase(value) && !"false".equalsIgnoreCase(value) &&
                        !"1".equals(value) && !"0".equals(value)) {
                        throw new IllegalArgumentException("布尔值格式错误，应为 true/false 或 1/0");
                    }
                    break;
                case "json":
                    // 简单的JSON格式验证
                    value = value.trim();
                    if (!(value.startsWith("{") && value.endsWith("}")) &&
                        !(value.startsWith("[") && value.endsWith("]"))) {
                        throw new IllegalArgumentException("JSON格式错误");
                    }
                    break;
                // string 和 date 类型暂不做格式验证
            }
        } catch (Exception e) {
            throw new RuntimeException("常数值格式验证失败: " + e.getMessage());
        }
    }
}
