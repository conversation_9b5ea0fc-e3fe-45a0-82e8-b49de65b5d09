package com.kbao.kbcelms.userorg.service;


import com.google.common.collect.Maps;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcelms.userorg.vo.UserOrgAddVO;
import com.kbao.kbcums.appsms.enums.IsDeleteEnum;
import com.kbao.tool.util.DateUtils;
import com.kbao.tool.util.EmptyUtils;
import com.kbao.tool.util.SysLoginUtils;
import org.springframework.stereotype.Service;
import com.kbao.kbcelms.userorg.entity.UserOrg;
import com.kbao.kbcelms.userorg.dao.UserOrgMapper;

import java.util.List;
import java.util.Map;


@Service
public class UserOrgService extends BaseSQLServiceImpl<UserOrg, Integer, UserOrgMapper> {


    public void saveOrUpdateUserOrg(UserOrgAddVO orgAddVO) {
        mapper.deleteByUserId(orgAddVO.getUserId(), orgAddVO.getTenantId());
        if ("dept".equals(orgAddVO.getOrgType())) {
            // 如果选择的总公司,删除所有的机构
            UserOrg userOrg = new UserOrg();
            userOrg.setUserId(orgAddVO.getUserId());
            userOrg.setTenantId(orgAddVO.getTenantId());
            userOrg.setOrgType(orgAddVO.getOrgType());
            userOrg.setIsDeleted(IsDeleteEnum.NO.getCode());
            userOrg.setCreateId(SysLoginUtils.getUserId());
            userOrg.setCreateTime(DateUtils.getCurrentDate());
            mapper.insert(userOrg);
        } else {
            orgAddVO.getUserOrgs().forEach(orgVO -> {
                UserOrg userOrg = mapper.findUserOrgByCondition(orgAddVO.getUserId(), orgAddVO.getTenantId(), orgVO.getOrgCode());
                if (EmptyUtils.isEmpty(userOrg)) {
                    userOrg = new UserOrg();
                    userOrg.setUserId(orgAddVO.getUserId());
                    userOrg.setTenantId(orgAddVO.getTenantId());
                    userOrg.setOrgType(orgAddVO.getOrgType());
                    userOrg.setOrganCode(orgVO.getOrgCode());
                    userOrg.setOrganName(orgVO.getOrgName());
                    userOrg.setIsDeleted(IsDeleteEnum.NO.getCode());
                    userOrg.setCreateId(SysLoginUtils.getUserId());
                    userOrg.setCreateTime(DateUtils.getCurrentDate());
                    mapper.insert(userOrg);
                } else if (IsDeleteEnum.YES.getCode().equals(userOrg.getIsDeleted())) {
                    userOrg.setIsDeleted(IsDeleteEnum.NO.getCode());
                    userOrg.setUpdateId(SysLoginUtils.getUserId());
                    userOrg.setUpdateTime(DateUtils.getCurrentDate());
                    mapper.updateByPrimaryKeySelective(userOrg);
                }
            });
        }
    }

    public List<UserOrg> findByUserId(String userId, String tenantId) {
        Map<String, Object> queryParam = Maps.newHashMap();
        queryParam.put("userId", userId);
        queryParam.put("tenantId", tenantId);
        return mapper.selectAll(queryParam);
    }
}
