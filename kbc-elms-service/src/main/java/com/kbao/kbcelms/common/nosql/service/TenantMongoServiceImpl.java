package com.kbao.kbcelms.common.nosql.service;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.kbao.kbcbsc.model.Pagination;
import com.kbao.kbcbsc.service.nosql.BaseMongoService;
import com.kbao.kbcelms.common.nosql.dao.TenantMongoDaoImpl;
import com.mongodb.client.result.UpdateResult;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.Serializable;
import java.util.*;

import java.util.ArrayList;
import java.util.List;

@Component
public abstract class TenantMongoServiceImpl<T, ID extends Serializable, D extends TenantMongoDaoImpl<T, ID>> implements BaseMongoService<T, ID, D> {

    protected Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    protected D dao;

    public TenantMongoServiceImpl() {
    }

    @PostConstruct
    protected void init() {
    }

    public D getDao() {
        return this.dao;
    }

    public T save(T entity) {
        return (T)this.dao.save(entity);
    }

    public T saveOrUpdate(T entity) {
        return (T)this.dao.saveOrUpdate(entity);
    }

    public T findById(ID id) {
        return (T)this.dao.findById(id);
    }

    public T findById(ID id, String collectionName) {
        return (T)this.dao.findById(id, collectionName);
    }

    public List<T> findAll() {
        return this.dao.findAll();
    }

    public List<T> findAll(String collectionName) {
        return this.dao.findAll(collectionName);
    }

    public List<T> find(Query query) {
        return this.dao.find(query);
    }

    public T findOne(Query query) {
        return (T)this.dao.findOne(query);
    }

    public long count(Query query) {
        return this.dao.count(query);
    }

    public UpdateResult update(Query query, Update update) {
        return this.dao.update(query, update);
    }

    public T updateOne(Query query, Update update) {
        return (T)this.dao.updateOne(query, update);
    }

    public UpdateResult update(T entity) {
        return this.dao.update(entity);
    }

    public T findAndModify(Query query, Update update, FindAndModifyOptions fmo) {
        return (T)this.dao.findAndModify(query, update, fmo);
    }

    public void remove(Query query) {
        this.dao.remove(query);
    }

    public void remove(ID id) {
        this.dao.remove(id);
    }

    public PageInfo<T> page(Query query, Pagination<T> page) {
        return this.page(query, page, (String)null, (String)null);
    }

    public PageInfo<T> page(Query query, Pagination<T> page, String defaultSorts) {
        return this.page(query, page, defaultSorts, (String)null);
    }

    public PageInfo<T> page(Query query, Pagination<T> page, String defaultSorts, String fields) {
        query = query == null ? new Query(Criteria.where("_id").exists(true)) : query;
        long count = this.dao.count(query);
        page.setTotal((long)((int)count));
        int pageNum = page.getPageNum();
        int pageSize = page.getPageSize();
        page.setPages((int)Math.ceil(((double)page.getTotal() + (double)0.0F) / (double)page.getPageSize()));
        String sorts = StringUtils.isBlank(page.getSort()) ? defaultSorts : page.getSort();
        if (StringUtils.isNotBlank(sorts)) {
            query.with(this.getMongoSort(sorts));
        }

        if (StringUtils.isNotBlank(fields)) {
            for(String field : fields.split(",")) {
                query.fields().include(field.trim());
            }
        }

        query.skip((long)((pageNum - 1) * pageSize)).limit(pageSize);
        List<T> rows = this.dao.find(query);
        page.addAll(rows);
        return new PageInfo(page);
    }

    protected Sort getMongoSort(String sorts) {
        String[] sortList = sorts.split(",");
        List<Sort.Order> orders = new ArrayList(sortList.length);

        for(String itemSort : sortList) {
            itemSort = itemSort.trim();
            String[] item = itemSort.split(" ");
            if (item.length == 1) {
                orders.add(new Sort.Order(Direction.ASC, item[0].trim()));
            } else if (item.length == 2) {
                orders.add(new Sort.Order(Direction.fromString(item[1].trim()), item[0]));
            }
        }

        return Sort.by(orders);
    }

    public List<T> find(Query query, String sorts) {
        return this.dao.find(query, sorts);
    }

    public List<T> find(Query query, String sorts, String fields) {
        return this.dao.find(query, sorts, fields);
    }

    public List findDistinct(String fieldName, Map qryMap) {
        return this.dao.findDistinct(fieldName, qryMap);
    }

    public List<JSONObject> aggregate(Aggregation aggregation, String collectionName) {
        return this.dao.aggregate(aggregation, collectionName);
    }
}