package com.kbao.kbcelms.opportunityorder.service;


import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.kbao.commons.exception.BusinessException;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcbsc.util.BscUserUtils;
import com.kbao.tool.util.DateUtils;
import com.kbao.tool.util.SysLoginUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.kbao.kbcelms.opportunityorder.entity.OpportunityOrder;
import com.kbao.kbcelms.opportunityorder.dao.OpportunityOrderMapper;


@Service
public class OpportunityOrderService extends BaseSQLServiceImpl<OpportunityOrder, Integer,OpportunityOrderMapper> {

    /**
     * 新增机会保单
     * @param opportunityOrder
     * @return
     */
    public OpportunityOrder addOpportunityOrder(OpportunityOrder opportunityOrder) {
        String tenantId = SysLoginUtils.getUser().getTenantId();
        opportunityOrder.setCreateId(BscUserUtils.getUserId());
        opportunityOrder.setCreateTime(DateUtils.getCurrentDate());
        opportunityOrder.setUpdateId(BscUserUtils.getUserId());
        opportunityOrder.setUpdateTime(DateUtils.getCurrentDate());
        opportunityOrder.setTenantId(tenantId);
        insert(opportunityOrder);
        return opportunityOrder;
    }

    /**
     * 修改机会保单
     * @param opportunityOrder
     * @return
     */
    public OpportunityOrder updateOpportunityOrder(OpportunityOrder opportunityOrder) {
        OpportunityOrder existingOrder = this.selectByPrimaryKey(opportunityOrder.getId());

        if (existingOrder == null) {
           throw new BusinessException("机会保单不存在");
        }

        if(opportunityOrder.getIsDeleted() == 1) {
            throw new BusinessException("机会保单已删除");
        }

        existingOrder.setOrderCode(opportunityOrder.getOrderCode());
        existingOrder.setPolicyNo(opportunityOrder.getPolicyNo());
        existingOrder.setCompanyCode(opportunityOrder.getCompanyCode());
        existingOrder.setUpdateId(BscUserUtils.getUserId());
        existingOrder.setUpdateTime(DateUtils.getCurrentDate());
        updateByPrimaryKey(existingOrder);
        return existingOrder;
    }

    /**
     * 删除机会保单
     * @param id
     */
    public void deleteOpportunityOrder(Integer id) {
        OpportunityOrder existingOrder = this.selectByPrimaryKey(id);
        if (existingOrder == null) {
            throw new BusinessException("机会保单不存在");
        }
        existingOrder.setIsDeleted(1);
        existingOrder.setUpdateId(BscUserUtils.getUserId());
        existingOrder.setUpdateTime(DateUtils.getCurrentDate());
        updateByPrimaryKey(existingOrder);
    }

    /**
     * 根据机会id查询机会保单列表
     * @param opportunityId
     * @return
     */
    public List<OpportunityOrder> queryOpportunityOrderListByOpportunityId(String opportunityId) {
        Map<String, Object> param = new HashMap<>();
        param.put("opportunityId", opportunityId);
        param.put("isDeleted", 0);
        return selectByParam(param);
    }
}
