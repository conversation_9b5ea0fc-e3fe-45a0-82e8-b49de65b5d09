package com.kbao.kbcelms.opportunityteam.dao;


import com.kbao.kbcbsc.dao.sql.BaseMapper;
import com.kbao.kbcelms.opportunityteam.entity.OpportunityTeam;
import com.kbao.kbcelms.opportunityteam.model.OpportunityTeamMember;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 机会成员接口
 */
public interface OpportunityTeamMapper  extends BaseMapper<OpportunityTeam, Integer>{

    /**
     * 查询机会项目成员
     *
     * @param opportunityId
     * @param tenantId
     * @return
     */
    List<OpportunityTeamMember> selectMember(@Param("opportunityId") Integer opportunityId,@Param("tenantId") String tenantId);

    /**
     * 获取单个项目成员
     *
     * @param id
     * @param roleType
     * @param opportunityId
     * @param tenantId
     * @return
     */
    OpportunityTeamMember findOneMember(@Param("id") Integer id,@Param("roleType") Integer roleType,@Param("opportunityId") Integer opportunityId,@Param("tenantId") String tenantId);


    /**
     * 查询项目经理
     *
     * @param opportunityId
     * @return
     */
    OpportunityTeam selectManager(@Param("opportunityId") Integer opportunityId);


    /**
     * 修改成员角色
     *
     * @param opportunityTeam
     */
    void updateMemberRole(OpportunityTeam opportunityTeam);


    /**
     * 根据机会id删除项目成员
     *
     * @param opportunityId
     */
    void deleteByOpportunityId(@Param("opportunityId") Integer opportunityId);
}